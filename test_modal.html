<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Test - WLM Category Classifier</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <h1 class="text-3xl font-bold text-center mb-8">Modal Functionality Test</h1>
            
            <div class="bg-white rounded-2xl shadow-xl p-8">
                <h2 class="text-xl font-semibold mb-4">Test Enhanced Processing Modal</h2>
                <p class="text-gray-600 mb-6">Click the button below to test the enhanced modal with real-time progress updates and logging.</p>
                
                <button onclick="testModal()" 
                        class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                    <i class="fas fa-magic mr-2"></i>
                    Test Enhanced Modal
                </button>
            </div>
        </div>
    </div>

    <!-- Enhanced Processing Modal (copied from index.html) -->
    <div id="processing-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50 transition-opacity duration-300">
        <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto transform transition-transform duration-300 scale-95">
            <div class="text-center mb-6">
                <div class="mb-4">
                    <div id="loading-spinner" class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
                    <div id="success-icon" class="hidden">
                        <i class="fas fa-check-circle text-6xl text-green-600 mx-auto"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-semibold text-gray-800 mb-2">Processing Your Data</h3>
                <p id="processing-message" class="text-lg text-gray-700 mb-4 font-medium">Preparing to analyze your file...</p>
                
                <!-- Main Progress Bar -->
                <div class="w-full bg-gray-200 rounded-full h-4 mb-2 shadow-inner">
                    <div id="progress-bar" class="bg-gradient-to-r from-blue-600 to-indigo-600 h-4 rounded-full transition-all duration-500 shadow-sm" style="width: 0%"></div>
                </div>
                <div class="text-sm text-gray-600 font-medium">
                    <span id="progress-text">0%</span> Complete
                </div>
            </div>

            <!-- Real-time Processing Log -->
            <div id="processing-log" class="mb-6 bg-gray-50 border border-gray-200 rounded-xl p-4 max-h-48 overflow-y-auto">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-terminal text-gray-600 mr-2"></i>
                    Processing Log
                </h4>
                <div id="log-entries" class="space-y-2 text-sm font-mono">
                    <div class="text-gray-600 flex items-center">
                        <span class="text-blue-600 mr-2">[INFO]</span>
                        <span class="text-xs text-gray-500 mr-2" id="log-timestamp"></span>
                        <span>Ready to start processing...</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Preview -->
            <div id="stats-preview" class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <h4 class="text-lg font-semibold text-blue-800 mb-3">
                    <i class="fas fa-chart-bar text-blue-600 mr-2"></i>
                    Live Processing Statistics
                </h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-products" class="text-2xl font-bold text-blue-600">-</div>
                        <div class="text-gray-600">Products Processed</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-weights" class="text-2xl font-bold text-green-600">-</div>
                        <div class="text-gray-600">Weights Extracted</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-predictions" class="text-2xl font-bold text-purple-600">-</div>
                        <div class="text-gray-600">Values Predicted</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-categories" class="text-2xl font-bold text-orange-600">-</div>
                        <div class="text-gray-600">Categories Assigned</div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div id="modal-actions" class="mt-6 flex justify-center space-x-4 hidden">
                <button onclick="alert('Results would be shown here')" class="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-2 px-6 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                    <i class="fas fa-eye mr-2"></i>
                    View Results
                </button>
                <button onclick="hideProcessingModal()" class="bg-gradient-to-r from-gray-600 to-gray-700 text-white py-2 px-6 rounded-lg font-semibold hover:from-gray-700 hover:to-gray-800 transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Close
                </button>
            </div>
        </div>
    </div>

    <script>
        // Test function to simulate the enhanced modal functionality
        function testModal() {
            showProcessingModal();
            simulateProcessing();
        }

        function showProcessingModal() {
            const modal = document.getElementById('processing-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');
            
            // Add smooth entrance animation
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.remove('scale-95');
                modal.querySelector('.bg-white').classList.add('scale-100');
            }, 10);
            
            // Initialize log with timestamp
            updateLogTimestamp();
        }

        function hideProcessingModal() {
            const modal = document.getElementById('processing-modal');
            
            // Add smooth exit animation
            modal.querySelector('.bg-white').classList.remove('scale-100');
            modal.querySelector('.bg-white').classList.add('scale-95');
            
            setTimeout(() => {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
                resetModalState();
            }, 300);
        }

        function simulateProcessing() {
            const steps = [
                { progress: 10, message: "Loading Excel file...", stats: { total_products: 1234 } },
                { progress: 20, message: "Successfully loaded 1234 product records", stats: { total_products: 1234 } },
                { progress: 30, message: "Analyzing data structure...", stats: { total_products: 1234 } },
                { progress: 45, message: "Initializing ML categorization engine...", stats: { total_products: 1234 } },
                { progress: 60, message: "Extracting unit weights from product names...", stats: { total_products: 1234, unit_weights_extracted: { count: 987 } } },
                { progress: 75, message: "Applying ML predictions for missing values...", stats: { total_products: 1234, unit_weights_extracted: { count: 987 }, case_weight_predictions: { count: 234 } } },
                { progress: 90, message: "Calculating case weights and capacities...", stats: { total_products: 1234, unit_weights_extracted: { count: 987 }, case_weight_predictions: { count: 234 }, pbl_categories: { unique_categories: 45 } } },
                { progress: 100, message: "Successfully processed 1234 products with enhanced categorization!", stats: { total_products: 1234, unit_weights_extracted: { count: 987 }, case_weight_predictions: { count: 234 }, pbl_categories: { unique_categories: 45 } } }
            ];

            let currentStep = 0;
            
            const processStep = () => {
                if (currentStep < steps.length) {
                    const step = steps[currentStep];
                    updateProgress(step.progress, step.message);
                    updateStatistics(step.stats);
                    addLogEntry('INFO', step.message);
                    
                    currentStep++;
                    
                    if (currentStep < steps.length) {
                        setTimeout(processStep, 1500); // 1.5 second delay between steps
                    } else {
                        // Processing complete
                        setTimeout(() => {
                            addLogEntry('SUCCESS', 'Processing completed successfully!');
                            showCompletionState();
                        }, 1000);
                    }
                }
            };

            // Start processing
            addLogEntry('INFO', 'Starting analysis process...');
            setTimeout(processStep, 500);
        }

        // Copy all the helper functions from index.html
        function updateProgress(progress, message) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const processingMessage = document.getElementById('processing-message');
            
            progressBar.style.width = progress + '%';
            progressText.textContent = progress + '%';
            processingMessage.textContent = message;
            
            if (progress >= 100) {
                progressBar.className = progressBar.className.replace(/from-\w+-\d+|to-\w+-\d+/g, '');
                progressBar.classList.add('from-green-600', 'to-emerald-600');
            }
        }

        function updateStatistics(stats) {
            if (!stats) return;
            
            if (stats.total_products !== undefined) {
                animateStatValue('stat-products', stats.total_products);
            }
            if (stats.unit_weights_extracted) {
                animateStatValue('stat-weights', stats.unit_weights_extracted.count || stats.unit_weights_extracted.percentage + '%');
            }
            if (stats.case_weight_predictions) {
                animateStatValue('stat-predictions', stats.case_weight_predictions.count || stats.case_weight_predictions.percentage + '%');
            }
            if (stats.pbl_categories) {
                animateStatValue('stat-categories', stats.pbl_categories.unique_categories || stats.pbl_categories.percentage + '%');
            }
        }

        function animateStatValue(elementId, newValue) {
            const element = document.getElementById(elementId);
            if (!element) return;
            
            element.style.transform = 'scale(1.1)';
            element.textContent = newValue;
            
            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
        }

        function addLogEntry(level, message) {
            const logEntries = document.getElementById('log-entries');
            const timestamp = new Date().toLocaleTimeString();
            
            const levelColors = {
                'INFO': 'text-blue-600',
                'SUCCESS': 'text-green-600',
                'ERROR': 'text-red-600',
                'WARNING': 'text-yellow-600'
            };
            
            const logEntry = document.createElement('div');
            logEntry.className = 'text-gray-600 flex items-center animate-fade-in';
            logEntry.innerHTML = `
                <span class="${levelColors[level] || 'text-gray-600'} mr-2">[${level}]</span>
                <span class="text-xs text-gray-500 mr-2">${timestamp}</span>
                <span>${message}</span>
            `;
            
            logEntries.appendChild(logEntry);
            
            const logContainer = document.getElementById('processing-log');
            logContainer.scrollTop = logContainer.scrollHeight;
            
            if (logEntries.children.length > 20) {
                logEntries.removeChild(logEntries.firstChild);
            }
        }

        function updateLogTimestamp() {
            const timestampElement = document.getElementById('log-timestamp');
            if (timestampElement) {
                timestampElement.textContent = new Date().toLocaleTimeString();
            }
        }

        function showCompletionState() {
            document.getElementById('loading-spinner').classList.add('hidden');
            document.getElementById('success-icon').classList.remove('hidden');
            document.getElementById('processing-message').textContent = 'Analysis completed successfully!';
            document.getElementById('modal-actions').classList.remove('hidden');
        }

        function resetModalState() {
            document.getElementById('loading-spinner').classList.remove('hidden');
            document.getElementById('success-icon').classList.add('hidden');
            document.getElementById('modal-actions').classList.add('hidden');
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-text').textContent = '0%';
            document.getElementById('processing-message').textContent = 'Preparing to analyze your file...';
            
            const logEntries = document.getElementById('log-entries');
            logEntries.innerHTML = `
                <div class="text-gray-600 flex items-center">
                    <span class="text-blue-600 mr-2">[INFO]</span>
                    <span class="text-xs text-gray-500 mr-2" id="log-timestamp"></span>
                    <span>Ready to start processing...</span>
                </div>
            `;
            
            ['stat-products', 'stat-weights', 'stat-predictions', 'stat-categories'].forEach(id => {
                document.getElementById(id).textContent = '-';
            });
            
            const progressBar = document.getElementById('progress-bar');
            progressBar.className = 'bg-gradient-to-r from-blue-600 to-indigo-600 h-4 rounded-full transition-all duration-500 shadow-sm';
        }
    </script>
</body>
</html>
