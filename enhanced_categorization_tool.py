"""
Enhanced Product Categorization Tool with Zero Value Prediction
Handles zero values in case_capacity and case_weight columns
"""

import pandas as pd
import numpy as np
import re
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import LabelEncoder

class ZeroValuePredictor:
    """Handles prediction of zero values in case_capacity and case_weight"""
    
    def __init__(self):
        self.case_capacity_stats = {}
        self.case_weight_stats = {}
        self.trained = False
        
    def train_from_data(self, training_df, input_df):
        """Train the predictor using both training and input data"""
        print("🔄 Training zero value predictor...")
        
        # Combine training and input data for better statistics
        combined_df = pd.concat([training_df, input_df], ignore_index=True)
        
        # Calculate case_capacity statistics by category
        self._calculate_case_capacity_stats(combined_df)
        
        # Calculate case_weight statistics by category  
        self._calculate_case_weight_stats(combined_df)
        
        self.trained = True
        print("✅ Zero value predictor trained")
        
    def _calculate_case_capacity_stats(self, df):
        """Calculate case_capacity statistics by hierarchical categories"""
        valid_df = df[df['case_capacity'] > 0]
        
        # Statistics by different category levels
        for col in ['DIV_DESC', 'DEP_DESC', 'SEC_DESC', 'GRP_DESC']:
            if col in valid_df.columns:
                stats = valid_df.groupby(col)['case_capacity'].agg([
                    'mean', 'median', 'std', 'count'
                ]).round(2)
                self.case_capacity_stats[col] = stats.to_dict('index')
                
        print(f"📊 Case capacity stats calculated for {len(self.case_capacity_stats)} category levels")
        
    def _calculate_case_weight_stats(self, df):
        """Calculate case_weight statistics by hierarchical categories"""
        valid_df = df[df['case_weight'] > 0]
        
        # Statistics by different category levels
        for col in ['DIV_DESC', 'DEP_DESC', 'SEC_DESC', 'GRP_DESC']:
            if col in valid_df.columns:
                stats = valid_df.groupby(col)['case_weight'].agg([
                    'mean', 'median', 'std', 'count'
                ]).round(2)
                self.case_weight_stats[col] = stats.to_dict('index')
                
        print(f"📊 Case weight stats calculated for {len(self.case_weight_stats)} category levels")
        
    def predict_case_capacity(self, row):
        """Predict case_capacity for a row with zero value"""
        if not self.trained:
            return None
            
        # Try different category levels in order of specificity
        for col in ['GRP_DESC', 'SEC_DESC', 'DEP_DESC', 'DIV_DESC']:
            if col in self.case_capacity_stats and col in row:
                category = row[col]
                if pd.notna(category) and category in self.case_capacity_stats[col]:
                    stats = self.case_capacity_stats[col][category]
                    
                    # Use median if available, otherwise mean
                    if stats['count'] >= 5:  # Only use if we have enough samples
                        predicted = stats['median'] if pd.notna(stats['median']) else stats['mean']
                        if predicted > 0:
                            return int(round(predicted))
        
        # Fallback: use overall median from training data
        return 8  # Common case capacity value
        
    def predict_case_weight(self, row):
        """Predict case_weight for a row with zero value"""
        if not self.trained:
            return None
            
        # First try to calculate from unit_weight * case_capacity if both available
        if pd.notna(row.get('unit_weight', None)) and pd.notna(row.get('case_capacity', None)):
            unit_weight = float(row['unit_weight'])
            case_capacity = float(row['case_capacity'])
            if unit_weight > 0 and case_capacity > 0:
                return unit_weight * case_capacity
                
        # Try different category levels in order of specificity
        for col in ['GRP_DESC', 'SEC_DESC', 'DEP_DESC', 'DIV_DESC']:
            if col in self.case_weight_stats and col in row:
                category = row[col]
                if pd.notna(category) and category in self.case_weight_stats[col]:
                    stats = self.case_weight_stats[col][category]
                    
                    # Use median if available, otherwise mean
                    if stats['count'] >= 5:  # Only use if we have enough samples
                        predicted = stats['median'] if pd.notna(stats['median']) else stats['mean']
                        if predicted > 0:
                            return predicted
        
        # Fallback: use unit_weight if available
        if pd.notna(row.get('unit_weight', None)):
            unit_weight = float(row['unit_weight'])
            if unit_weight > 0:
                return unit_weight * 8  # Assume typical case capacity of 8
                
        # Final fallback
        return 1.0  # 1kg default case weight


class EnhancedWeightRulesProductCategorizationTool:
    """Enhanced version with zero value prediction capabilities"""

    def __init__(self):
        # Weight extraction patterns (supporting European decimal format)
        self.weight_patterns = [
            (r'(\d+(?:[,.]\d+)?)\s*x\s*(\d+(?:[,.]\d+)?)\s*g(?:ram)?s?\b', lambda m: self._parse_decimal(m.group(1)) * self._parse_decimal(m.group(2))),
            (r'(\d+(?:[,.]\d+)?)\s*g(?:ram)?s?\b', lambda m: self._parse_decimal(m.group(1))),
            (r'(\d+(?:[,.]\d+)?)\s*x\s*(\d+(?:[,.]\d+)?)\s*ml\b', lambda m: self._parse_decimal(m.group(1)) * self._parse_decimal(m.group(2))),
            (r'(\d+(?:[,.]\d+)?)\s*ml\b', lambda m: self._parse_decimal(m.group(1))),
            (r'(\d+(?:[,.]\d+)?)\s*x\s*(\d+(?:[,.]\d+)?)\s*l\b', lambda m: self._parse_decimal(m.group(1)) * self._parse_decimal(m.group(2)) * 1000),
            (r'(\d+(?:[,.]\d+)?)\s*l(?:itre)?s?\b', lambda m: self._parse_decimal(m.group(1)) * 1000),
            (r'(\d+(?:[,.]\d+)?)\s*kg\b', lambda m: self._parse_decimal(m.group(1)) * 1000),
        ]

        # ML components
        self.vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
        self.classifier = RandomForestClassifier(n_estimators=100, random_state=42)
        self.is_trained = False

        # Weight rules from groups sheet - will be loaded from Excel
        self.weight_rules = {}

        # Zero value predictor
        self.zero_predictor = ZeroValuePredictor()

    def _parse_decimal(self, decimal_str):
        """Parse European decimal format (comma as decimal separator)"""
        return float(decimal_str.replace(',', '.'))

    def _is_reasonable_weight(self, weight_grams):
        """Check if extracted weight is reasonable (between 1g and 50kg)"""
        return 1 <= weight_grams <= 50000

    def extract_unit_weight(self, product_name):
        """Extract unit weight in grams from product name"""
        if pd.isna(product_name):
            return None

        product_name = str(product_name).lower()
        for pattern, calc_func in self.weight_patterns:
            match = re.search(pattern, product_name, re.IGNORECASE)
            if match:
                try:
                    weight = calc_func(match)
                    if self._is_reasonable_weight(weight):
                        return weight
                except:
                    continue
        return None

    def _load_weight_rules(self, training_file_path):
        """Load weight rules from the groups sheet"""
        print("📋 Loading weight rules from groups sheet...")

        try:
            groups_df = pd.read_excel(training_file_path, sheet_name='groups', engine='pyxlsb')
            print(f"✅ Loaded {len(groups_df)} group definitions")

            # Parse weight rules from the Weight? column
            weight_rules = {}

            for _, row in groups_df.iterrows():
                group_name = str(row.get('Group name', '')).strip()
                weight_desc = str(row.get('Weight?', '')).strip().lower()

                if group_name and weight_desc and weight_desc != 'nan':
                    # Parse different weight formats
                    min_weight, max_weight = self._parse_weight_range(weight_desc)
                    if min_weight is not None or max_weight is not None:
                        weight_rules[group_name] = {
                            'min_weight': min_weight,
                            'max_weight': max_weight,
                            'description': weight_desc
                        }
                        print(f"  {group_name}: {min_weight}-{max_weight}kg ({weight_desc})")

            self.weight_rules = weight_rules
            print(f"✅ Loaded {len(weight_rules)} weight rules")

        except Exception as e:
            print(f"⚠️  Could not load weight rules: {e}")
            print("Using default weight rules...")
            self._create_default_weight_rules()

    def _parse_weight_range(self, weight_desc):
        """Parse weight range from description"""
        weight_desc = weight_desc.lower().strip()

        # Handle different formats
        if 'over 8kg' in weight_desc or 'above 8' in weight_desc:
            return 8.0, None
        elif 'over 13' in weight_desc:
            return 13.0, None
        elif 'over 15' in weight_desc:
            return 15.0, None
        elif '8-20kg' in weight_desc:
            return 8.0, 20.0
        elif '8-16kg' in weight_desc:
            return 8.0, 16.0
        elif '8-15kg' in weight_desc:
            return 8.0, 15.0
        elif '1.5-8kg' in weight_desc or '1,5-8kg' in weight_desc:
            return 1.5, 8.0
        elif '4-8kg' in weight_desc or '4-8' in weight_desc:
            return 4.0, 8.0
        elif '0-4 kg' in weight_desc or '0-4kg' in weight_desc:
            return 0.0, 4.0
        elif 'under 6kg' in weight_desc or 'usually under 6kg' in weight_desc:
            return 0.0, 6.0
        elif 'average 2kg' in weight_desc:
            return 0.0, 4.0  # Assume 2kg ± 2kg range
        elif 'average 4-8kg' in weight_desc:
            return 4.0, 8.0
        elif 'average 5kg' in weight_desc:
            return 2.0, 8.0  # Assume 5kg ± 3kg range
        elif 'average 6kg' in weight_desc or 'around 6kg' in weight_desc:
            return 3.0, 9.0  # Assume 6kg ± 3kg range

        return None, None

    def _create_default_weight_rules(self):
        """Create default weight rules if groups sheet not available"""
        self.weight_rules = {
            'Alcohol & spirits light': {'min_weight': 1.5, 'max_weight': 8.0, 'description': '1.5-8kg'},
            'Alcohol & spirits heavy': {'min_weight': 8.0, 'max_weight': 20.0, 'description': '8-20kg'},
            'Health & Beauty 0-4': {'min_weight': 0.0, 'max_weight': 4.0, 'description': '0-4 kg'},
            'Health & Beauty 4-8': {'min_weight': 4.0, 'max_weight': 8.0, 'description': '4-8kg'},
            'Light Laundry & Cleaning 0-4': {'min_weight': 0.0, 'max_weight': 4.0, 'description': '0-4 kg'},
            'Light Laundry & Cleaning 4-8': {'min_weight': 4.0, 'max_weight': 8.0, 'description': '4-8kg'},
            'Heavy Laundry & Cleaning & H&B': {'min_weight': 8.0, 'max_weight': None, 'description': 'over 8kg'},
            'Deli and dairy 0-4': {'min_weight': 0.0, 'max_weight': 4.0, 'description': '0-4 kg'},
            'Deli and dairy 4-8': {'min_weight': 4.0, 'max_weight': 8.0, 'description': '4-8 kg'},
        }

    def predict_zero_values(self, df):
        """Predict zero values in case_capacity and case_weight"""
        print("🔄 Predicting zero values...")

        if not self.zero_predictor.trained:
            print("⚠️  Zero predictor not trained - skipping zero value prediction")
            return df

        # Track predictions
        capacity_predictions = 0
        weight_predictions = 0

        # Create copies of the columns to modify
        df = df.copy()
        df['original_case_capacity'] = df['case_capacity'].copy()
        df['original_case_weight'] = df['case_weight'].copy()
        df['predicted_case_capacity'] = False
        df['predicted_case_weight'] = False

        # Predict case_capacity for zero values
        zero_capacity_mask = (df['case_capacity'] == 0)
        for idx in df[zero_capacity_mask].index:
            row = df.loc[idx]
            predicted_capacity = self.zero_predictor.predict_case_capacity(row)
            if predicted_capacity is not None:
                df.loc[idx, 'case_capacity'] = predicted_capacity
                df.loc[idx, 'predicted_case_capacity'] = True
                capacity_predictions += 1

        # Predict case_weight for zero values (after case_capacity prediction)
        zero_weight_mask = (df['case_weight'] == 0)
        for idx in df[zero_weight_mask].index:
            row = df.loc[idx]
            predicted_weight = self.zero_predictor.predict_case_weight(row)
            if predicted_weight is not None:
                df.loc[idx, 'case_weight'] = predicted_weight
                df.loc[idx, 'predicted_case_weight'] = True
                weight_predictions += 1

        print(f"✅ Predicted {capacity_predictions} case_capacity values")
        print(f"✅ Predicted {weight_predictions} case_weight values")

        return df

    def process_products_with_zero_prediction(self, df, training_file_path, pbl_column_name):
        """Complete processing pipeline with zero value prediction"""
        print("🔧 Starting enhanced product processing pipeline with zero value prediction...")

        # Step 1: Load training data and train zero predictor
        print("🔄 Loading training data...")
        training_df = pd.read_excel(training_file_path, engine='pyxlsb')
        print(f"✅ Loaded {len(training_df)} training records")

        # Train zero value predictor
        self.zero_predictor.train_from_data(training_df, df)

        # Step 2: Predict zero values FIRST
        df = self.predict_zero_values(df)

        # Step 3: Extract unit weights
        print("🔄 Extracting unit weights...")
        df['extracted_unit_weight'] = df['product_name'].apply(self.extract_unit_weight)

        # Step 4: Correct unit weights (choose best between existing and extracted)
        print("🔄 Correcting unit weights...")
        def correct_unit_weight(row):
            extracted = row['extracted_unit_weight']
            existing = row.get('unit_weight', None)

            if pd.notna(extracted):
                return float(extracted)
            elif pd.notna(existing):
                try:
                    return float(existing) * 1000  # Convert kg to grams
                except (ValueError, TypeError):
                    return None
            return None

        df['corrected_unit_weight_g'] = df.apply(correct_unit_weight, axis=1)

        # Step 5: Calculate case weights (using predicted values if available)
        print("🔄 Calculating case weights...")
        def calculate_case_weight(row):
            try:
                if pd.notna(row['corrected_unit_weight_g']) and pd.notna(row.get('case_capacity', None)):
                    unit_weight = float(row['corrected_unit_weight_g'])
                    case_capacity = float(row['case_capacity'])
                    return unit_weight * case_capacity
                elif pd.notna(row.get('case_weight', None)):
                    return float(row['case_weight']) * 1000  # Convert kg to grams
            except (ValueError, TypeError):
                pass
            return None

        df['calculated_case_weight'] = df.apply(calculate_case_weight, axis=1)

        # Step 6: Load weight rules
        self._load_weight_rules(training_file_path)

        # Step 7: Train ML model (using enhanced training data preparation)
        print("🔄 Training ML model...")
        enhanced_training_df = self._prepare_training_features(training_df)
        enhanced_training_df = enhanced_training_df.dropna(subset=[pbl_column_name, 'calculated_case_weight'])
        print(f"📊 Training with {len(enhanced_training_df)} valid records")

        # Prepare features
        X, y = self._create_features(enhanced_training_df, pbl_column_name)

        # Create combined feature matrix
        text_features = self.vectorizer.fit_transform(X['text_features'])
        numerical_features = np.column_stack([
            X['case_weight'].reshape(-1, 1),
            X['hierarchical_features'].reshape(-1, 1)
        ])
        combined_features = np.hstack([text_features.toarray(), numerical_features])

        # Train the classifier
        self.classifier.fit(combined_features, y)
        print(f"✅ Model trained on {len(y)} samples with {combined_features.shape[1]} features")
        self.is_trained = True

        # Print training accuracy
        y_pred = self.classifier.predict(combined_features)
        accuracy = accuracy_score(y, y_pred)
        print(f"✅ Model trained with accuracy: {accuracy:.3f}")
        print(f"📋 Categories learned: {len(np.unique(y))}")

        # Step 8: Predict PBL categories with weight rule corrections
        df['predicted_pbl_category'] = self.predict_pbl_category(df)

        print("✅ Enhanced processing pipeline with zero value prediction completed!")
        return df

    def _prepare_training_features(self, df):
        """Prepare training data with calculated case weights"""
        # Extract unit weights from product names
        df['extracted_unit_weight'] = df['product_name'].apply(self.extract_unit_weight)

        # Use extracted or existing unit weight (training data uses 'unit weight' with space)
        def get_unit_weight(row):
            if pd.notna(row['extracted_unit_weight']):
                return float(row['extracted_unit_weight'])
            elif pd.notna(row.get('unit weight', None)):  # Training data column name
                try:
                    return float(row['unit weight']) * 1000  # Convert kg to grams
                except (ValueError, TypeError):
                    return None
            return None

        df['unit_weight_g'] = df.apply(get_unit_weight, axis=1)

        # Calculate case weights (training data uses 'case_capacity' and 'case weight')
        def calculate_case_weight(row):
            try:
                if pd.notna(row['unit_weight_g']) and pd.notna(row.get('case_capacity', None)):
                    unit_weight = float(row['unit_weight_g'])
                    case_capacity = float(row['case_capacity'])
                    return unit_weight * case_capacity
                elif pd.notna(row.get('case weight', None)):  # Training data column name
                    return float(row['case weight']) * 1000  # Convert kg to grams
            except (ValueError, TypeError):
                pass
            return None

        df['calculated_case_weight'] = df.apply(calculate_case_weight, axis=1)

        return df

    def _create_features(self, df, pbl_column):
        """Create feature matrix for ML training"""
        # Text features: product name + hierarchical categories
        text_features = []
        hierarchical_features = []
        case_weights = []

        for _, row in df.iterrows():
            # Combine text features (use 'product_name' for both training and prediction data)
            text_parts = [str(row.get('product_name', ''))]
            hier_parts = []

            for col in ['DIV_DESC', 'DEP_DESC', 'SEC_DESC', 'GRP_DESC', 'SGR_DESC']:
                if col in row and pd.notna(row[col]):
                    text_parts.append(str(row[col]))
                    hier_parts.append(str(row[col]).lower())

            text_features.append(' '.join(text_parts))
            hierarchical_features.append(len(hier_parts))  # Hierarchy depth as feature

            # Handle case weight conversion with error checking
            try:
                case_weight_value = row.get('calculated_case_weight', 0)
                if pd.notna(case_weight_value):
                    case_weights.append(float(case_weight_value) / 1000.0)  # Convert to kg
                else:
                    case_weights.append(0.0)
            except (ValueError, TypeError):
                case_weights.append(0.0)

        X = {
            'text_features': text_features,
            'case_weight': np.array(case_weights),
            'hierarchical_features': np.array(hierarchical_features)
        }

        # Only get y values if pbl_column exists in the dataframe
        if pbl_column in df.columns:
            y = df[pbl_column].values
        else:
            y = None

        return X, y

    def predict_pbl_category(self, df):
        """Predict PBL categories for new products with weight rule corrections"""
        if not self.is_trained:
            raise ValueError("Model must be trained before making predictions")

        print("🔄 Predicting PBL categories...")

        # Create features (no PBL column needed for prediction)
        X, _ = self._create_features(df, None)  # No PBL column for prediction

        # Transform text features
        text_features = self.vectorizer.transform(X['text_features'])

        # Combine features
        numerical_features = np.column_stack([
            X['case_weight'].reshape(-1, 1),
            X['hierarchical_features'].reshape(-1, 1)
        ])
        combined_features = np.hstack([text_features.toarray(), numerical_features])

        # Get initial predictions
        raw_predictions = self.classifier.predict(combined_features)

        # Apply weight-based corrections using groups sheet rules
        corrected_predictions = self._apply_weight_rules(df, raw_predictions)

        print(f"✅ Predicted PBL categories for {len(corrected_predictions)} products")
        return corrected_predictions

    def _apply_weight_rules(self, df, predictions):
        """Apply weight rules from groups sheet to correct predictions"""
        print("📏 Applying weight rules from groups sheet...")
        corrected_predictions = predictions.copy()
        corrections = 0

        for i, (_, row) in enumerate(df.iterrows()):
            case_weight_kg = row.get('calculated_case_weight', 0) / 1000.0
            current_prediction = predictions[i]

            # Get product type keywords
            product_keywords = [
                str(row.get('product_name', '')),
                str(row.get('DEP_DESC', '')),
                str(row.get('SEC_DESC', ''))
            ]

            # Find weight-appropriate category
            suggested_category = self._find_weight_appropriate_category(case_weight_kg, product_keywords)

            if suggested_category and suggested_category != current_prediction:
                # Check if current prediction violates weight rules
                if current_prediction in self.weight_rules:
                    rule = self.weight_rules[current_prediction]
                    min_w = rule['min_weight'] or 0
                    max_w = rule['max_weight'] or float('inf')

                    # If weight is outside the rule range, correct it
                    if not (min_w <= case_weight_kg <= max_w):
                        corrected_predictions[i] = suggested_category
                        corrections += 1
                        print(f"  Fixed: {row.get('product_name', '')[:40]}... {case_weight_kg:.1f}kg: {current_prediction} → {suggested_category}")

        print(f"  Applied {corrections} weight rule corrections")
        return corrected_predictions

    def _find_weight_appropriate_category(self, case_weight_kg, product_type_keywords):
        """Find the weight-appropriate category for a product"""

        # Define product type to category mapping
        type_category_mapping = {
            'alcohol': ['Alcohol & spirits light', 'Alcohol & spirits heavy'],
            'health_beauty': ['Health & Beauty 0-4', 'Health & Beauty 4-8'],
            'cleaning': ['Light Laundry & Cleaning 0-4', 'Light Laundry & Cleaning 4-8', 'Heavy Laundry & Cleaning & H&B'],
            'dairy': ['Deli and dairy 0-4', 'Deli and dairy 4-8'],
            'laundry': ['Light Laundry & Cleaning 0-4', 'Light Laundry & Cleaning 4-8', 'Heavy Laundry & Cleaning & H&B']
        }

        # Detect product type from keywords
        detected_type = None
        text_lower = ' '.join(product_type_keywords).lower()

        if any(word in text_lower for word in ['alcohol', 'wine', 'spirits', 'whiskey', 'whisky', 'vodka', 'beer', 'liker']):
            detected_type = 'alcohol'
        elif any(word in text_lower for word in ['deo', 'beauty', 'cosmetic', 'shampoo', 'cream']):
            detected_type = 'health_beauty'
        elif any(word in text_lower for word in ['cleaning', 'detergent', 'laundry', 'domestos', 'wash']):
            detected_type = 'cleaning'
        elif any(word in text_lower for word in ['dairy', 'cheese', 'milk', 'yogurt', 'deli']):
            detected_type = 'dairy'

        # Find appropriate weight category
        if detected_type and detected_type in type_category_mapping:
            possible_categories = type_category_mapping[detected_type]

            # Find the category that matches the weight
            for category in possible_categories:
                if category in self.weight_rules:
                    rule = self.weight_rules[category]
                    min_w = rule['min_weight'] or 0
                    max_w = rule['max_weight'] or float('inf')

                    if min_w <= case_weight_kg <= max_w:
                        return category

            # If no exact match, use the closest category
            if case_weight_kg < 4:
                return next((cat for cat in possible_categories if '0-4' in cat), possible_categories[0])
            elif case_weight_kg < 8:
                return next((cat for cat in possible_categories if '4-8' in cat), possible_categories[-1])
            else:
                return next((cat for cat in possible_categories if 'heavy' in cat.lower() or '8' in cat), possible_categories[-1])

        return None
