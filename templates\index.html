<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WLM Category Classifier</title>
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <header class="text-center mb-12">
            <div class="inline-block p-6 bg-white rounded-full shadow-lg mb-6 animate-bounce">
                <i class="fas fa-chart-line text-4xl text-blue-600"></i>
            </div>
            <h1 class="text-5xl font-bold text-gray-800 mb-4 animate-fade-in">
                WLM Category Classifier
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Advanced product data analysis with ML-powered weight extraction, 
                zero value prediction, and intelligent PBL categorization
            </p>
        </header>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto">
            <!-- Upload Section -->
            <div id="upload-section" class="bg-white rounded-2xl shadow-xl p-8 mb-8">
                <div class="text-center">
                    <div class="mb-6">
                        <i class="fas fa-cloud-upload-alt text-6xl text-blue-500 mb-4"></i>
                        <h2 class="text-3xl font-semibold text-gray-800 mb-2">Upload Your Data</h2>
                        <p class="text-gray-600">Drop your Excel file here or click to browse</p>
                    </div>

                    <form id="upload-form" hx-post="/upload" hx-encoding="multipart/form-data"
                          hx-target="#upload-result" hx-indicator="#upload-spinner" class="space-y-6"
                          onsubmit="return handleFormSubmission(event)">
                        
                        <!-- File Drop Zone -->
                        <div class="border-2 border-dashed border-blue-300 rounded-xl p-12 bg-blue-50 hover:bg-blue-100 transition-all duration-300 cursor-pointer"
                             onclick="document.getElementById('file-input').click()">
                            <input type="file" id="file-input" name="file" accept=".xlsx,.xls" 
                                   class="hidden" onchange="updateFileName(this)">
                            <div id="drop-text">
                                <i class="fas fa-file-excel text-4xl text-blue-400 mb-3"></i>
                                <p class="text-lg font-medium text-blue-700">Click to select Excel file</p>
                                <p class="text-sm text-blue-600 mt-1">Supports .xlsx and .xls files</p>
                            </div>
                        </div>

                        <!-- Upload Button -->
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-4 px-8 rounded-xl font-semibold text-lg hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg">
                            <span id="upload-spinner" class="htmx-indicator">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                            </span>
                            <i class="fas fa-magic mr-2"></i>
                            Analyze & Categorize Products
                        </button>
                    </form>

                    <!-- Upload Result -->
                    <div id="upload-result" class="mt-6"></div>
                </div>
            </div>

            <!-- Features Section -->
            <div class="grid md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-weight-hanging text-2xl text-green-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Weight Extraction</h3>
                    <p class="text-gray-600">Automatically extracts and corrects unit weights from product names using advanced regex patterns</p>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-brain text-2xl text-purple-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">ML Prediction</h3>
                    <p class="text-gray-600">Uses machine learning to predict missing case capacities and weights based on hierarchical categories</p>
                </div>

                <div class="bg-white rounded-xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow duration-300">
                    <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-tags text-2xl text-orange-600"></i>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-2">Smart Categorization</h3>
                    <p class="text-gray-600">Intelligent PBL category assignment using trained models with hierarchical database knowledge</p>
                </div>
            </div>

            <!-- Requirements Section -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-8">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    File Requirements
                </h3>
                <div class="grid md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <h4 class="font-semibold text-gray-700 mb-2">Required Columns:</h4>
                        <ul class="space-y-1 text-gray-600">
                            <li><i class="fas fa-check text-green-500 mr-2"></i>product_name (or similar)</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>case_capacity</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>case_weight</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>unit_weight</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-700 mb-2">Supported Formats:</h4>
                        <ul class="space-y-1 text-gray-600">
                            <li><i class="fas fa-file-excel text-green-500 mr-2"></i>Excel (.xlsx)</li>
                            <li><i class="fas fa-file-excel text-green-500 mr-2"></i>Excel (.xls)</li>
                            <li><i class="fas fa-info text-blue-500 mr-2"></i>Max size: 100MB</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Processing Modal -->
    <div id="processing-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50 transition-opacity duration-300">
        <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto transform transition-transform duration-300 scale-95">
            <div class="text-center mb-6">
                <div class="mb-4">
                    <div id="loading-spinner" class="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto"></div>
                    <div id="success-icon" class="hidden">
                        <i class="fas fa-check-circle text-6xl text-green-600 mx-auto"></i>
                    </div>
                </div>
                <h3 class="text-2xl font-semibold text-gray-800 mb-2">Processing Your Data</h3>
                <p id="processing-message" class="text-lg text-gray-700 mb-4 font-medium">Preparing to analyze your file...</p>

                <!-- Main Progress Bar -->
                <div class="w-full bg-gray-200 rounded-full h-4 mb-2 shadow-inner">
                    <div id="progress-bar" class="bg-gradient-to-r from-blue-600 to-indigo-600 h-4 rounded-full transition-all duration-500 shadow-sm" style="width: 0%"></div>
                </div>
                <div class="text-sm text-gray-600 font-medium">
                    <span id="progress-text">0%</span> Complete
                </div>
            </div>

            <!-- Real-time Processing Log -->
            <div id="processing-log" class="mb-6 bg-gray-50 border border-gray-200 rounded-xl p-4 max-h-48 overflow-y-auto">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">
                    <i class="fas fa-terminal text-gray-600 mr-2"></i>
                    Processing Log
                </h4>
                <div id="log-entries" class="space-y-2 text-sm font-mono">
                    <div class="text-gray-600 flex items-center">
                        <span class="text-blue-600 mr-2">[INFO]</span>
                        <span class="text-xs text-gray-500 mr-2" id="log-timestamp"></span>
                        <span>Waiting for file upload...</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Statistics Preview -->
            <div id="stats-preview" class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                <h4 class="text-lg font-semibold text-blue-800 mb-3">
                    <i class="fas fa-chart-bar text-blue-600 mr-2"></i>
                    Live Processing Statistics
                </h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-products" class="text-2xl font-bold text-blue-600">-</div>
                        <div class="text-gray-600">Products Processed</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-weights" class="text-2xl font-bold text-green-600">-</div>
                        <div class="text-gray-600">Weights Extracted</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-predictions" class="text-2xl font-bold text-purple-600">-</div>
                        <div class="text-gray-600">Values Predicted</div>
                    </div>
                    <div class="bg-white rounded-lg p-3 text-center transition-all duration-300 hover:shadow-md">
                        <div id="stat-categories" class="text-2xl font-bold text-orange-600">-</div>
                        <div class="text-gray-600">Categories Assigned</div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div id="modal-actions" class="mt-6 flex justify-center space-x-4 hidden">
                <button onclick="viewResults()" class="bg-gradient-to-r from-green-600 to-emerald-600 text-white py-2 px-6 rounded-lg font-semibold hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
                    <i class="fas fa-eye mr-2"></i>
                    View Results
                </button>
                <button onclick="hideProcessingModal()" class="bg-gradient-to-r from-gray-600 to-gray-700 text-white py-2 px-6 rounded-lg font-semibold hover:from-gray-700 hover:to-gray-800 transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Close
                </button>
            </div>
        </div>
    </div>

    <script src="/static/js/app.js"></script>
    <script>
        function updateFileName(input) {
            const file = input.files[0];
            if (file) {
                const dropText = document.getElementById('drop-text');
                dropText.innerHTML = `
                    <i class="fas fa-file-excel text-4xl text-green-500 mb-3"></i>
                    <p class="text-lg font-medium text-green-700">${file.name}</p>
                    <p class="text-sm text-green-600 mt-1">Ready to process</p>
                `;
            }
        }

        // Global variable to store current task ID
        let currentTaskId = null;

        // Handle form submission with immediate modal display
        function handleFormSubmission(event) {
            // Validate file selection first
            const fileInput = document.getElementById('file-input');
            if (!fileInput.files || fileInput.files.length === 0) {
                event.preventDefault();
                showNotification('Please select a file first', 'error');
                return false;
            }

            // Show modal immediately before form submission
            showProcessingModal();
            addLogEntry('INFO', 'File selected, starting upload...');

            // Allow form submission to continue
            return true;
        }

        // Handle form submission
        document.getElementById('upload-form').addEventListener('htmx:afterRequest', function(e) {
            if (e.detail.xhr.status === 200) {
                const response = JSON.parse(e.detail.xhr.responseText);
                if (response.task_id) {
                    currentTaskId = response.task_id;
                    addLogEntry('SUCCESS', `File uploaded successfully. Task ID: ${response.task_id}`);
                    addLogEntry('INFO', 'Starting background processing...');
                    pollTaskStatus(response.task_id);
                } else {
                    addLogEntry('ERROR', 'Upload failed - no task ID received');
                    hideProcessingModal();
                }
            } else {
                addLogEntry('ERROR', `Upload failed with status: ${e.detail.xhr.status}`);
                hideProcessingModal();
            }
        });

        function showProcessingModal() {
            const modal = document.getElementById('processing-modal');
            modal.classList.remove('hidden');
            modal.classList.add('flex');

            // Add smooth entrance animation
            setTimeout(() => {
                modal.querySelector('.bg-white').classList.remove('scale-95');
                modal.querySelector('.bg-white').classList.add('scale-100');
            }, 10);

            // Initialize log with timestamp
            updateLogTimestamp();
        }

        function hideProcessingModal() {
            const modal = document.getElementById('processing-modal');

            // Add smooth exit animation
            modal.querySelector('.bg-white').classList.remove('scale-100');
            modal.querySelector('.bg-white').classList.add('scale-95');

            setTimeout(() => {
                modal.classList.add('hidden');
                modal.classList.remove('flex');
                resetModalState();
            }, 300);
        }

        function pollTaskStatus(taskId) {
            const poll = setInterval(async () => {
                try {
                    const response = await fetch(`/status/${taskId}`);
                    const data = await response.json();

                    updateProgress(data.progress, data.message);
                    updateStatistics(data.stats);
                    addLogEntry('INFO', data.message);

                    if (data.status === 'completed') {
                        clearInterval(poll);
                        addLogEntry('SUCCESS', 'Processing completed successfully!');
                        showCompletionState();

                        setTimeout(() => {
                            window.location.href = `/results/${taskId}`;
                        }, 2000);
                    } else if (data.status === 'error') {
                        clearInterval(poll);
                        addLogEntry('ERROR', data.error || 'Unknown error occurred');
                        showErrorState();
                    }
                } catch (error) {
                    console.error('Polling error:', error);
                    addLogEntry('ERROR', `Connection error: ${error.message}`);
                }
            }, 1000);
        }

        function updateProgress(progress, message) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            const processingMessage = document.getElementById('processing-message');

            // Smooth progress bar animation
            progressBar.style.width = progress + '%';
            progressText.textContent = progress + '%';
            processingMessage.textContent = message;

            // Change progress bar color based on progress
            if (progress >= 100) {
                progressBar.className = progressBar.className.replace(/from-\w+-\d+|to-\w+-\d+/g, '');
                progressBar.classList.add('from-green-600', 'to-emerald-600');
            }
        }

        function updateStatistics(stats) {
            if (!stats) return;

            // Update statistics with animation
            if (stats.total_products !== undefined) {
                animateStatValue('stat-products', stats.total_products);
            }
            if (stats.unit_weights_extracted) {
                animateStatValue('stat-weights', stats.unit_weights_extracted.count || stats.unit_weights_extracted.percentage + '%');
            }
            if (stats.case_weight_predictions) {
                animateStatValue('stat-predictions', stats.case_weight_predictions.count || stats.case_weight_predictions.percentage + '%');
            }
            if (stats.pbl_categories) {
                animateStatValue('stat-categories', stats.pbl_categories.unique_categories || stats.pbl_categories.percentage + '%');
            }
        }

        function animateStatValue(elementId, newValue) {
            const element = document.getElementById(elementId);
            if (!element) return;

            element.style.transform = 'scale(1.1)';
            element.textContent = newValue;

            setTimeout(() => {
                element.style.transform = 'scale(1)';
            }, 200);
        }

        function addLogEntry(level, message) {
            const logEntries = document.getElementById('log-entries');
            const timestamp = new Date().toLocaleTimeString();

            const levelColors = {
                'INFO': 'text-blue-600',
                'SUCCESS': 'text-green-600',
                'ERROR': 'text-red-600',
                'WARNING': 'text-yellow-600'
            };

            const logEntry = document.createElement('div');
            logEntry.className = 'text-gray-600 flex items-center animate-fade-in';
            logEntry.innerHTML = `
                <span class="${levelColors[level] || 'text-gray-600'} mr-2">[${level}]</span>
                <span class="text-xs text-gray-500 mr-2">${timestamp}</span>
                <span>${message}</span>
            `;

            logEntries.appendChild(logEntry);

            // Auto-scroll to bottom
            const logContainer = document.getElementById('processing-log');
            logContainer.scrollTop = logContainer.scrollHeight;

            // Limit log entries to prevent memory issues
            if (logEntries.children.length > 50) {
                logEntries.removeChild(logEntries.firstChild);
            }
        }

        function updateLogTimestamp() {
            const timestampElement = document.getElementById('log-timestamp');
            if (timestampElement) {
                timestampElement.textContent = new Date().toLocaleTimeString();
            }
        }

        function showCompletionState() {
            // Hide spinner, show success icon
            document.getElementById('loading-spinner').classList.add('hidden');
            document.getElementById('success-icon').classList.remove('hidden');

            // Update message
            document.getElementById('processing-message').textContent = 'Analysis completed successfully!';

            // Show action buttons
            document.getElementById('modal-actions').classList.remove('hidden');
        }

        function showErrorState() {
            // Hide spinner
            document.getElementById('loading-spinner').classList.add('hidden');

            // Update message
            document.getElementById('processing-message').textContent = 'An error occurred during processing';

            // Show close button only
            const actions = document.getElementById('modal-actions');
            actions.innerHTML = `
                <button onclick="hideProcessingModal()" class="bg-gradient-to-r from-red-600 to-red-700 text-white py-2 px-6 rounded-lg font-semibold hover:from-red-700 hover:to-red-800 transition-all duration-200">
                    <i class="fas fa-times mr-2"></i>
                    Close
                </button>
            `;
            actions.classList.remove('hidden');
        }

        function resetModalState() {
            // Reset all modal elements to initial state
            document.getElementById('loading-spinner').classList.remove('hidden');
            document.getElementById('success-icon').classList.add('hidden');
            document.getElementById('modal-actions').classList.add('hidden');
            document.getElementById('progress-bar').style.width = '0%';
            document.getElementById('progress-text').textContent = '0%';
            document.getElementById('processing-message').textContent = 'Preparing to analyze your file...';

            // Clear log entries
            const logEntries = document.getElementById('log-entries');
            logEntries.innerHTML = `
                <div class="text-gray-600 flex items-center">
                    <span class="text-blue-600 mr-2">[INFO]</span>
                    <span class="text-xs text-gray-500 mr-2" id="log-timestamp"></span>
                    <span>Waiting for file upload...</span>
                </div>
            `;

            // Reset statistics
            ['stat-products', 'stat-weights', 'stat-predictions', 'stat-categories'].forEach(id => {
                document.getElementById(id).textContent = '-';
            });

            // Reset progress bar color
            const progressBar = document.getElementById('progress-bar');
            progressBar.className = 'bg-gradient-to-r from-blue-600 to-indigo-600 h-4 rounded-full transition-all duration-500 shadow-sm';
        }

        function viewResults() {
            if (currentTaskId) {
                window.location.href = `/results/${currentTaskId}`;
            }
        }

        // Simple notification function (fallback if not available from app.js)
        function showNotification(message, type = 'info') {
            // Try to use the notification from app.js first
            if (typeof window.showNotification === 'function') {
                window.showNotification(message, type);
                return;
            }

            // Fallback: simple alert
            if (type === 'error') {
                alert('Error: ' + message);
            } else {
                console.log(`[${type.toUpperCase()}] ${message}`);
            }
        }
    </script>
</body>
</html>