"""
WLM Category Classifier Web Application
FastAPI + HTMX modern web app for product data analysis and categorization
"""

import os
import uuid
import asyncio
from typing import Optional
from datetime import datetime
from pathlib import Path

import pandas as pd
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, FileResponse
from starlette.background import BackgroundTask

from enhanced_categorization_tool import EnhancedWeightRulesProductCategorizationTool

app = FastAPI(title="WLM Category Classifier", version="1.0.0")

# Setup static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Directories for file handling
UPLOAD_DIR = Path("uploads")
DOWNLOAD_DIR = Path("downloads")
UPLOAD_DIR.mkdir(exist_ok=True)
DOWNLOAD_DIR.mkdir(exist_ok=True)

# Store processing status
processing_status = {}

class ProcessingTask:
    def __init__(self, task_id: str, filename: str):
        self.task_id = task_id
        self.filename = filename
        self.status = "pending"
        self.progress = 0
        self.message = "Initializing..."
        self.result_file = None
        self.error = None
        self.stats = {}
        self.started_at = datetime.now()

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/upload")
async def upload_file(background_tasks: BackgroundTasks, file: UploadFile = File(...)):
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="Only Excel files (.xlsx, .xls) are supported")
    
    # Generate unique task ID
    task_id = str(uuid.uuid4())
    
    # Save uploaded file
    file_path = UPLOAD_DIR / f"{task_id}_{file.filename}"
    content = await file.read()
    with open(file_path, "wb") as f:
        f.write(content)
    
    # Initialize processing task
    task = ProcessingTask(task_id, file.filename)
    processing_status[task_id] = task
    
    # Start background processing
    background_tasks.add_task(process_file_background, task_id, file_path)
    
    return {"task_id": task_id, "filename": file.filename, "status": "uploaded"}

@app.get("/status/{task_id}")
async def get_status(task_id: str):
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task = processing_status[task_id]
    return {
        "task_id": task_id,
        "status": task.status,
        "progress": task.progress,
        "message": task.message,
        "result_file": task.result_file,
        "error": task.error,
        "stats": task.stats,
        "started_at": task.started_at.isoformat()
    }

@app.get("/results/{task_id}")
async def get_results(request: Request, task_id: str):
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task = processing_status[task_id]
    
    # Load sample data for preview if task is completed
    sample_data = []
    if task.status == "completed" and task.result_file:
        try:
            result_path = Path("downloads") / task.result_file
            print(f"Attempting to read: {result_path}")
            print(f"File exists: {result_path.exists()}")
            
            if result_path.exists():
                df = pd.read_excel(result_path)
                print(f"Excel file loaded, columns: {list(df.columns)[:10]}")
                print(f"Dataframe shape: {df.shape}")
                
                # Get first 5 rows as sample
                sample_rows = df.head(5).to_dict('records')
                print(f"Sample rows extracted: {len(sample_rows)}")
                
                for i, row in enumerate(sample_rows):
                    print(f"Processing row {i}: {list(row.keys())[:5]}")
                    
                    # Handle unit weight display
                    original_weight = row.get('unit_weight', '')
                    if pd.isna(original_weight) or original_weight == '':
                        original_weight = '-'
                    
                    # Handle extracted weight display
                    extracted_weight = row.get('extracted_unit_weight', '')
                    if pd.isna(extracted_weight) or extracted_weight == '':
                        extracted_weight = row.get('corrected_unit_weight_g', '')
                    if pd.isna(extracted_weight) or extracted_weight == '':
                        extracted_weight = '-'
                    
                    # Handle case capacity
                    case_capacity = row.get('case_capacity', '')
                    if pd.isna(case_capacity):
                        case_capacity = '-'
                    
                    # Handle PBL category
                    pbl_category = row.get('predicted_pbl_category', '')
                    if pd.isna(pbl_category) or pbl_category == '':
                        pbl_category = '-'
                    
                    sample_data.append({
                        'product_name': row.get('product_name', ''),
                        'original_weight': str(original_weight),
                        'extracted_weight': str(extracted_weight),
                        'case_capacity': str(case_capacity),
                        'pbl_category': str(pbl_category)
                    })
            else:
                print(f"Result file not found: {result_path}")
                
        except ImportError as e:
            print(f"Missing required packages: {e}")
            # Fallback: provide sample data structure
            sample_data = [{
                'product_name': 'Sample data requires pandas/openpyxl installation',
                'original_weight': '-',
                'extracted_weight': '-', 
                'case_capacity': '-',
                'pbl_category': '-'
            }]
        except Exception as e:
            print(f"Error loading sample data: {e}")
            print(f"Task status: {task.status}, Result file: {task.result_file}")
    
    task.sample_data = sample_data
    print(f"Sample data loaded: {len(sample_data)} rows")
    return templates.TemplateResponse("results.html", {
        "request": request, 
        "task": task
    })

@app.get("/download/{task_id}")
async def download_result(task_id: str):
    if task_id not in processing_status:
        raise HTTPException(status_code=404, detail="Task not found")
    
    task = processing_status[task_id]
    if not task.result_file or task.status != "completed":
        raise HTTPException(status_code=400, detail="File not ready for download")
    
    file_path = DOWNLOAD_DIR / task.result_file
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Result file not found")
    
    return FileResponse(
        path=file_path,
        filename=f"processed_{task.filename}",
        media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    )

async def process_file_background(task_id: str, file_path: Path):
    """Background task to process the uploaded file"""
    task = processing_status[task_id]
    
    try:
        task.status = "processing"
        task.message = "Loading file..."
        task.progress = 10
        
        # Load the Excel file
        df = pd.read_excel(file_path)
        task.progress = 20
        task.message = f"Loaded {len(df)} records"
        
        # Check for required columns
        if 'product_name' not in df.columns:
            potential_cols = [col for col in df.columns if any(x in col.lower() for x in ['name', 'product', 'desc'])]
            if potential_cols:
                df = df.rename(columns={potential_cols[0]: 'product_name'})
                task.message = f"Using '{potential_cols[0]}' as product name column"
            else:
                task.error = "No product name column found. Please ensure your file has a 'product_name' column or similar."
                task.status = "error"
                return
        
        task.progress = 30
        task.message = "Initializing categorization tool..."
        
        # Initialize the enhanced categorization tool
        tool = EnhancedWeightRulesProductCategorizationTool()
        training_file_path = 'hier_database_v2.xlsb'
        pbl_column_name = 'PBL category'
        
        task.progress = 40
        task.message = "Processing products with ML categorization..."
        
        # Process the data
        enhanced_df = tool.process_products_with_zero_prediction(df, training_file_path, pbl_column_name)
        
        task.progress = 80
        task.message = "Generating statistics and saving results..."
        
        # Calculate statistics
        stats = calculate_processing_stats(enhanced_df)
        task.stats = stats
        
        # Save result file
        result_filename = f"{task_id}_processed.xlsx"
        result_path = DOWNLOAD_DIR / result_filename
        enhanced_df.to_excel(result_path, index=False)
        
        task.result_file = result_filename
        task.progress = 100
        task.status = "completed"
        task.message = "Processing completed successfully!"
        
        # Clean up uploaded file
        if file_path.exists():
            os.remove(file_path)
            
    except Exception as e:
        task.status = "error"
        task.error = str(e)
        task.message = f"Error: {str(e)}"

def calculate_processing_stats(df):
    """Calculate statistics for the processed data"""
    stats = {
        "total_products": len(df),
        "columns_processed": list(df.columns)
    }
    
    # Weight extraction stats
    if 'extracted_unit_weight' in df.columns:
        extracted_count = df['extracted_unit_weight'].notna().sum()
        stats["unit_weights_extracted"] = {
            "count": int(extracted_count),
            "percentage": round(extracted_count/len(df)*100, 1)
        }
    
    # Corrected weights stats
    if 'corrected_unit_weight_g' in df.columns:
        corrected_count = df['corrected_unit_weight_g'].notna().sum()
        stats["unit_weights_corrected"] = {
            "count": int(corrected_count),
            "percentage": round(corrected_count/len(df)*100, 1)
        }
    
    # Case weight calculations
    if 'calculated_case_weight' in df.columns:
        case_weight_count = df['calculated_case_weight'].notna().sum()
        stats["case_weights_calculated"] = {
            "count": int(case_weight_count),
            "percentage": round(case_weight_count/len(df)*100, 1)
        }
    
    # Zero value predictions
    if 'predicted_case_capacity' in df.columns:
        capacity_predictions = df['predicted_case_capacity'].sum()
        stats["case_capacity_predictions"] = {
            "count": int(capacity_predictions),
            "percentage": round(capacity_predictions/len(df)*100, 1)
        }
    
    if 'predicted_case_weight' in df.columns:
        weight_predictions = df['predicted_case_weight'].sum()
        stats["case_weight_predictions"] = {
            "count": int(weight_predictions),
            "percentage": round(weight_predictions/len(df)*100, 1)
        }
    
    # PBL category stats
    if 'predicted_pbl_category' in df.columns:
        category_counts = df['predicted_pbl_category'].value_counts()
        stats["pbl_categories"] = {
            "total_assigned": int(df['predicted_pbl_category'].notna().sum()),
            "percentage": round(df['predicted_pbl_category'].notna().sum()/len(df)*100, 1),
            "unique_categories": len(category_counts),
            "top_categories": category_counts.head(10).to_dict()
        }
    
    return stats

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)